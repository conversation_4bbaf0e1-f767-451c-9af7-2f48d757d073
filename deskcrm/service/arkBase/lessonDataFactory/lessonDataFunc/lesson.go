package lessonDataFunc

import (
	"deskcrm/api/achilles"
	"deskcrm/api/dal"
	"deskcrm/api/das"
	"deskcrm/api/dataproxy"
	"deskcrm/api/jxexamui"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"fmt"
	"slices"
	"time"

	"github.com/gin-gonic/gin"
)

func (s *Format) GetLessonName(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonName := ""
		if _, ok := lessonMap[lessonID]; ok {
			lessonName = lessonMap[lessonID].LessonName
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonName)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节名称】 ", "dal courseinfo")
	return
}

// GetType 获取章节类型
// 对应PHP中的type字段，来源于lessonType
func (s *Format) GetType(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonType := 0
		if _, ok := lessonMap[lessonID]; ok {
			lessonType = lessonMap[lessonID].LessonType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节类型】", "dal courseinfo")
	return
}

// GetPlayType 获取播放类型
// 对应PHP中的playType字段
func (s *Format) GetPlayType(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playType := 0
		if _, ok := lessonMap[lessonID]; ok {
			playType = lessonMap[lessonID].PlayType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取播放类型】", "dal courseinfo")
	return
}

// GetInclassTime 获取上课时间
// 对应PHP中的inclassTime字段，格式化为"YYYY-MM-DD HH:MM-HH:MM"
func (s *Format) GetInclassTime(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		inclassTime := ""
		if _, ok := lessonMap[lessonID]; ok {
			startTime := lessonMap[lessonID].StartTime
			stopTime := lessonMap[lessonID].StopTime
			// 格式化为 "2006-01-02 15:04-15:04" 格式，对应PHP的LESSON_INCLASS_TIME
			startTimeStr := time.Unix(int64(startTime), 0).Format("2006-01-02 15:04")
			stopTimeStr := time.Unix(int64(stopTime), 0).Format("15:04")
			inclassTime = startTimeStr + "-" + stopTimeStr
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取上课时间】", "dal courseinfo")
	return
}

// GetStopTime 获取章节结束时间
// 对应PHP中的stopTime字段
func (s *Format) GetStopTime(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		stopTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			stopTime = lessonMap[lessonID].StopTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, stopTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节结束时间】", "dal courseinfo")
	return
}

// GetStartTime 获取章节开始时间
// 对应PHP中的startTime字段
func (s *Format) GetStartTime(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		startTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			startTime = lessonMap[lessonID].StartTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, startTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节开始时间】", "dal courseinfo")
	return
}

// GetPreview 获取预习数据
// 对应PHP中的preview字段
func (s *Format) GetPreview(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	// 获取基础LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取课程信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)
	gradeId := int(courseData.MainGradeId)
	subjectId := int(courseData.MainSubjectId)

	// 获取学段信息
	gradeStage := define.Grade2XB[gradeId]

	// 获取iLab信息（仅针对初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	ilabData := ilabInfo.(*dataQuery.ILabInfo)

	// 获取预习开启状态信息
	previewOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetPreviewOpenInfo", []interface{}{
		s.param.CourseID, s.param.LessonIDs, gradeStage,
	})
	if err != nil {
		return
	}
	previewOpenData := previewOpenInfo.(map[int64]dataQuery.PreviewOpenInfo)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化预习数据数组：[显示文本, 颜色, 是否可点击]
		previewArray := LessonDataArray("-", "gray", 1)

		if lessonLuData, ok := luData[lessonID]; ok {
			correctNum := lessonLuData.PreviewCorrectNum
			participateNum := lessonLuData.PreviewParticipateNum
			totalNum := lessonLuData.PreviewTotalNum

			// 基础预习数据格式化
			if totalNum == 0 {
				previewArray[0] = "-"
			} else {
				previewArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, totalNum)
			}

			// iLab兼容逻辑 - 针对初二物理课程
			if gradeId == 3 && subjectId == 4 {
				if checkInfo, exists := ilabData.CheckIlabLesson[lessonID]; exists && checkInfo.ILabLesson > 0 {
					if level, hasLevel := ilabData.PreviewInfoByIlab[lessonID]; hasLevel {
						if levelText, ok := jxexamui.LevelIlabMap[level]; ok && levelText != "" {
							previewArray[0] = levelText
							// 根据iLab等级设置颜色
							switch level {
							case 1:
								previewArray[1] = "green" // 优秀
							case 2:
								previewArray[1] = "orange" // 良好
							}
						} else {
							previewArray[0] = "-"
						}
					}
				}
			}

			// 预习开启状态检查
			if previewArray[0] == "-" {
				if openInfo, exists := previewOpenData[lessonID]; exists {
					if openInfo.IsOpenPreview == 1 {
						// 检查预习完成状态
						if lessonLuData.IsPreviewFinish == 1 {
							previewArray[0] = "0/0/0"
						} else {
							previewArray[0] = "未提交"
						}
					} else {
						// 预习未开启，显示"-"
						previewArray[0] = "-"
					}
				}
				previewArray[2] = 0
			}
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, previewArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取预习数据】", "LU: previewCorrectNum, previewParticipateNum, previewTotalNum, isPreviewFinish; iLab兼容; 预习开启状态检查")
	return
}

// GetAttendData 获取到课数据
// 对应PHP中的attend字段
func (s *Format) GetAttendData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"attend_duration"}) {
		return
	}

	// 获取学生课程数据 - attendDuration字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取学生章节请假信息
	lessonStudentData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	studentLeaveData := lessonStudentData.(map[int64]*models.LessonStudent)

	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := lessonInfoData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		// 获取attendDuration
		attendDuration := int64(0)
		if lessonData, ok := luData[lessonID]; ok {
			attendDuration = lessonData.AttendDuration
		}

		// 获取playType
		playType := int64(0)
		if lesson, ok := lessonMap[lessonID]; ok {
			playType = int64(lesson.PlayType)
		}

		attend := FormatDuration(attendDuration)
		if playType == dal.PLAY_TYPE_LUBOKE {
			attend = "-"
			attendDuration = 0
		}

		attendCode := 0
		leaveSeason := ""
		if studentInfo, ok := studentLeaveData[lessonID]; ok {
			if studentInfo.PreAttend == models.PreAttendLeave {
				attendCode = 3
				if studentInfo.ExtData.LeaveSeason != "" {
					leaveSeason = studentInfo.ExtData.LeaveSeason
				}
			}
		} else {
			attendCode = 0
		}

		// 添加到课数据
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attend)
		// 添加到课状态码和请假原因
		_ = s.AddOutputStudent(ctx, lessonID, "attendCode", attendCode)
		_ = s.AddOutputStudent(ctx, lessonID, "leaveSeason", leaveSeason)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【直播到课时长】", "ES: attendDuration, DAL: playType, DB: tblLessonStudent学生请假信息")
	return
}

// GetPlayback 获取回放数据
// 对应PHP中的playback字段，格式化为"XminYs"或"-"（录播课程）
func (s *Format) GetPlayback(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"playback_time", "playback_time_after_unlock"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"playbackTinclass_teacher_room_total_playback_time_v1otalTime"}) {
		return
	}

	// 获取LU数据 - 回放时长相关字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	queryData2, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonluData := queryData2.(map[int64]*dataproxy.GetCommonLuResp)

	// 获取章节基础信息 - t007Tag和playType字段
	lessonBaseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonBaseInfo", []interface{}{s.param.LessonIDs})
	if err != nil {
		return
	}
	lessonInfoMap := lessonBaseInfo.(map[int64]*achilles.ProcessedLessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playback := "-"
		playbackV1 := ""

		if lessonLuData, ok := luData[lessonID]; ok {
			var playbackTotalTime int64

			// 根据t007Tag决定使用哪个回放时长
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.T007Tag == 1 {
				playbackTotalTime = lessonLuData.PlaybackTimeAfterUnlock
			} else {
				playbackTotalTime = lessonLuData.PlaybackTotalTime
			}
			// 检查是否为录播课程
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.PlayType == dal.PLAY_TYPE_LUBOKE {
				playback = "-"
			} else {
				// 格式化回放时长
				playback = FormatDuration(playbackTotalTime)
			}
		}

		if commonLessonData, ok := commonluData[lessonID]; ok {
			playbackV1 = FormatDuration(commonLessonData.InclassTeacherRoomTotalPlaybackTimeV1)
		}

		// 输出回放时长
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playback)
		_ = s.AddOutputStudent(ctx, lessonID, "playbackV1", playbackV1)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【回放时长】", "LU: playbackTotalTime, playbackTimeAfterUnlock + Achilles: t007Tag, playType")
	return
}

// GetPlaybackOnlineTimeV1 获取回放观看时长(新)
// 对应PHP中的playbackv1字段，格式化为"XminYs"
func (s *Format) GetPlaybackOnlineTimeV1(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"inclass_teacher_room_total_playback_time_v1"}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	for _, lessonID := range s.param.LessonIDs {
		playbackV1Data := ""

		if lessonData, ok := luData[lessonID]; ok {
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			playbackV1Data = FormatDuration(lessonData.InclassTeacherRoomTotalPlaybackTimeV1)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playbackV1Data)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取回放观看时长(新)】", "ES: inclass_teacher_room_total_playback_time_v1")
	return
}

// GetLbpAttendDuration 获取LBP观看时长
// 对应PHP中的lbpAttendDuration字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDuration(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_teacher_room_total_playback_content_time"}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonLuData := queryData.(map[int64]*dataproxy.GetCommonLuResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDuration := ""
		if lessonData, ok := commonLuData[lessonID]; ok {
			lbpAttendDuration = FormatDuration(lessonData.InclassTeacherToomTotalPlaybackContentTime)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDuration)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长】", "CommonLU: inclass_teacher_room_total_playback_content_time")
	return
}

// GetLbpAttendDurationOld 获取LBP观看时长(旧版)
// 对应PHP中的lbpAttendDurationOld字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDurationOld(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"lbp_attend_duration"}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDurationOld := ""

		if lessonData, ok := luData[lessonID]; ok {
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			lbpAttendDurationOld = FormatDuration(lessonData.LbpAttendDuration)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDurationOld)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长(旧版)】", "ES: lbp_attend_duration")
	return
}

// GetOralQuestion 获取口述题数据
// 对应PHP中的oralQuestion字段，包含复杂的状态判断逻辑
func (s *Format) GetOralQuestion(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"bind_id", "relation_type", "bind_status", "is_artificial_correct"}) {
		return
	}

	// 获取LU数据 - 口述题相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取试卷绑定数据 - 口述题绑定信息
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.RelationTypeLesson,
		[]int{dataQuery.BindTypeOralQuestion},
	})
	if err != nil {
		return
	}
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化口述题数据数组：[显示文本, 颜色, 是否可点击]
		oralQuestionArray := LessonDataArray("-", "gray", 0)
		status := consts.OralQuStatusUnknown

		// 检查是否有口述题绑定
		if examInfo, exists := examRelationData[lessonID][dataQuery.BindTypeOralQuestion]; exists {
			bindStatus := false
			isArtificialCorrect := false

			if examInfo.BindStatus > 0 {
				bindStatus = true
			}

			if examInfo.IsArtificialCorrect > 0 {
				isArtificialCorrect = true
			}

			if bindStatus {
				// 获取学生口述题数据
				if lessonLuData, exists := luData[lessonID]; exists {
					oralQuestionSubmit := lessonLuData.OralQuestionSubmit > 0
					oralQuestionCorrectTime := lessonLuData.OralQuestionCorrectTime

					if oralQuestionSubmit {
						status = consts.OralQuStatusSubmit
						// 需要批改且没有批改（根据时间判断），则展示待批改
						if isArtificialCorrect && oralQuestionCorrectTime <= 0 {
							status = consts.OralQuStatusTbCorrected
						}
					} else {
						status = consts.OralQuStatusUnsubmit
					}
				} else {
					status = consts.OralQuStatusUnsubmit
				}
			}
		}
		// 设置显示文本
		oralQuestionArray[0] = consts.OralQuStatusMap[status]

		// 设置颜色和可点击状态
		if status == consts.OralQuStatusSubmit || status == consts.OralQuStatusTbCorrected {
			oralQuestionArray[1] = consts.ColorGreen
			oralQuestionArray[2] = 1 // 可点击
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, oralQuestionArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取口述题数据】", "LU: oralQuestionSubmit, oralQuestionCorrectTime + Exam: bind_status, is_artificial_correct")
	return
}

// GetInclassTest 获取堂堂测数据
// 对应PHP中的inclassTest字段
func (s *Format) GetInclassTest(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"bind_id", "relation_type", "bindStatus", "total_num"}) {
		return
	}

	// 获取LU数据 - 堂堂测相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取考试绑定数据 - 堂堂测总数
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.BindTypeTestInClass,
		[]int{dataQuery.RelationTypeLesson},
	})
	if err != nil {
		return
	}
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	// 获取课程信息 - 用于获取年级和学科信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{
		s.param.CourseID,
	})
	if err != nil {
		return
	}
	course := courseInfo.(dal.CourseInfo)
	gradeId := course.MainGradeId
	subjectId := course.MainSubjectId

	var ilab *dataQuery.ILabInfo
	if slices.Contains(consts.AcceptGradeSlice, gradeId) && slices.Contains(consts.AcceptSubjectSlice, subjectId) {
		// 获取ILab信息
		ilabInfo, ilabErr := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{s.param.LessonIDs, s.param.StudentUid})
		if ilabErr != nil {
			return ilabErr
		}
		ilab = ilabInfo.(*dataQuery.ILabInfo)
	}

	for _, lessonID := range s.param.LessonIDs {
		// 初始化堂堂测数据数组：[显示文本, 颜色, 是否可点击]
		inclassTestArray := LessonDataArray("-", "gray", 1)

		// 获取堂堂测总数
		tangTangTotalNum := int64(0)
		if examInfo, exists := examRelationData[lessonID][dataQuery.BindTypeTestInClass]; exists {
			if examInfo.TotalNum > 0 && examInfo.BindStatus > 0 {
				tangTangTotalNum = examInfo.TotalNum
			}
		}

		// 获取学生堂堂测数据
		if lessonLuData, exists := luData[lessonID]; exists {
			correctNum := lessonLuData.TangTangExamCorrectNum
			participateNum := lessonLuData.TangTangExamParticipateNum

			inclassTestArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, tangTangTotalNum)

			if tangTangTotalNum > 0 {
				// 设置颜色：满分绿色，不满分橙色
				if correctNum == tangTangTotalNum {
					inclassTestArray[1] = consts.COLOR_GREEN
				} else if correctNum < tangTangTotalNum {
					inclassTestArray[1] = consts.COLOR_ORANGE
				}
			}

			// 处理ILab兼容逻辑（初二物理课程）
			if course.MainGradeId == consts.ILAB_GRADE_ID && course.MainSubjectId == consts.ILAB_SUBJECT_ID && ilab != nil {
				// 检查是否为ILab章节
				if ilabLessonInfo, exists := ilab.CheckIlabLesson[lessonID]; exists && ilabLessonInfo.ILabLesson > 0 {
					// ILab 逻辑：使用ILab的堂堂测信息
					if ilabInclassTestStatus, exists := ilab.InclassTestInfoByIlab[lessonID]; exists {
						// 有ILab堂堂测状态，根据状态设置显示文本和颜色
						if levelText, exists := consts.ILAB_LEVEL_MAP[ilabInclassTestStatus]; exists {
							inclassTestArray[0] = levelText
							// 根据等级设置颜色
							switch ilabInclassTestStatus {
							case 1:
								inclassTestArray[1] = consts.COLOR_GREEN
							case 2:
								inclassTestArray[1] = consts.COLOR_ORANGE
							}
						}
					}
				}
			}

		}

		// 如果显示为"-"，设置不可点击
		if inclassTestArray[0] == "-" {
			inclassTestArray[2] = 0
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTestArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取堂堂测数据】", "LU: tangTangExamCorrectNum, tangTangExamParticipateNum, tangTangExamScore, isTangTangExamSubmit + Exam: total_num + CheckIsHx + ILab")
	return
}

// GetHomeworkData 获取作业数据
// 对应PHP中的homework字段，格式化为["状态", "颜色", "是否可点击"]
func (s *Format) GetHomeworkData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}

	// 获取DAS数据 - 作业状态和订正状态
	dasQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonsData", []interface{}{
		[]int64{s.param.StudentUid}, s.param.LessonIDs,
	})
	if err != nil {
		return err
	}

	// 获取作业绑定数据
	hwBindExams, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkBindExams", []interface{}{
		s.param.LessonIDs,
		consts.BindTypeHomework, // 作业绑定类型
	})
	if err != nil {
		return err
	}

	// 判断是否为订正项目课程
	isHac, err := s.dataQueryPoint.GetInstanceData(ctx, "IsHomeworkAmendCourse", []interface{}{s.param.CourseID})
	if err != nil {
		return err
	}

	// 获取作业开启信息
	homeworkOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkOpenInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return err
	}

	// 获取课程信息 - 用于获取年级和学科信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{
		s.param.CourseID,
	})
	if err != nil {
		return err
	}

	// 获取ILab信息（如果是初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取需要审核的课程映射
	lessonNeedAuditMap, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonNeedAuditMap", []interface{}{s.param.LessonIDs})
	if err != nil {
		return err
	}

	// 类型转换
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)
	dasData := dasQueryData.(map[int64]map[int64]*das.StudentLessonInfo)
	bindExams := hwBindExams.(map[int64]bool)
	openInfo := homeworkOpenInfo.(map[int64]jxexamui.HomeworkOpenInfo)
	ilab := ilabInfo.(*dataQuery.ILabInfo)
	course := courseInfo.(dal.CourseInfo)
	isAmendCourse := isHac.(bool)
	auditMap := lessonNeedAuditMap.(map[int64]bool)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化作业数据数组：[显示文本, 颜色, 是否可点击]
		homeworkArray := LessonDataArray("-", "gray", 1)

		// 获取当前章节的数据
		dasStudentLessonInfo := dasData[s.param.StudentUid][lessonID]
		isBindHw := bindExams[lessonID]
		homeworkOpenStatus := openInfo[lessonID]
		luData := luData[lessonID]

		// 兼容das作业未布置错误问题
		if dasStudentLessonInfo != nil && dasStudentLessonInfo.HomeworkStatus == 0 && isBindHw {
			dasStudentLessonInfo.HomeworkStatus = 1
		}

		// 使用consts包中的作业等级映射
		homeworkLevelMap := consts.HomeworkLevelMap

		// 处理订正课程逻辑
		if isAmendCourse {
			// 检查作业是否开启
			if homeworkOpenStatus.IsOpen != consts.HomeworkOpen {
				homeworkArray[0] = consts.StatusDash
			} else if !isBindHw {
				homeworkArray[0] = consts.StatusNotAssigned
			} else if luData != nil && luData.HomeworkLevel > 0 {
				// 有作业等级，显示等级
				if levelText, exists := homeworkLevelMap[luData.HomeworkLevel]; exists {
					homeworkArray[0] = levelText
				} else {
					homeworkArray[0] = consts.StatusNoLevel
				}
			} else if dasStudentLessonInfo != nil {
				// 处理订正状态
				switch dasStudentLessonInfo.HomeworkRecorrect {
				case 2:
					homeworkArray[0] = consts.StatusWaitGrade
					// 检查是否需要审核
					if auditMap[lessonID] {
						homeworkArray[0] = consts.StatusWaitAudit
					}
				case 4:
					homeworkArray[0] = consts.StatusWaitRegrade
				case 5:
					homeworkArray[0] = consts.StatusWaitResubmit
				default:
					homeworkArray[0] = consts.StatusNotSubmitted
				}
			}
		} else {
			// 检查作业是否开启
			if homeworkOpenStatus.IsOpen != consts.HomeworkOpen {
				homeworkArray[0] = consts.StatusDash
			} else if !isBindHw {
				homeworkArray[0] = consts.StatusNotAssigned
			} else if dasStudentLessonInfo != nil {
				switch dasStudentLessonInfo.HomeworkStatus {
				case 1:
					homeworkArray[0] = consts.StatusNotSubmitted
				case 2:
					homeworkArray[0] = consts.StatusNotGraded
				case 3:
					// 已批改，显示等级
					if luData != nil && luData.HomeworkLevel > 0 {
						if levelText, exists := homeworkLevelMap[luData.HomeworkLevel]; exists {
							homeworkArray[0] = levelText
						} else {
							homeworkArray[0] = consts.StatusNoLevel
						}
					} else {
						homeworkArray[0] = consts.StatusNoLevel
					}
				default:
					homeworkArray[0] = consts.StatusDash
				}
			}
		}

		// 设置颜色
		if homeworkArray[0] == consts.HomeworkFullMarksCode {
			homeworkArray[1] = "green" // 满分
		} else {
			// 检查是否为其他等级（不满分）
			for _, levelText := range homeworkLevelMap {
				if homeworkArray[0] == levelText && homeworkArray[0] != consts.HomeworkFullMarksCode {
					homeworkArray[1] = "orange" // 不满分
					break
				}
			}
		}

		// 处理ILab兼容逻辑（初二物理课程）
		if course.MainGradeId == consts.ILabGradeId && course.MainSubjectId == consts.ILabSubjectId && ilab != nil {
			// 检查是否为ILab v2章节
			if ilabLessonInfo, exists := ilab.CheckIlabLesson[lessonID]; exists && ilabLessonInfo.Version == consts.ILabVersion2 {
				// ILab v2 逻辑：使用ILab的作业信息
				if ilabHomeworkStatus, exists := ilab.HomeworkInfoByIlab[lessonID]; exists && ilabHomeworkStatus > 0 {
					// 有ILab作业状态，根据状态设置显示文本
					switch ilabHomeworkStatus {
					case 1:
						homeworkArray[0] = consts.StatusNotSubmitted
					case 2:
						homeworkArray[0] = consts.StatusNotGraded
					case 3:
						// 已批改，但ILab的等级信息需要从其他地方获取
						// 暂时显示已批改状态
						homeworkArray[0] = consts.StatusGraded
					default:
						homeworkArray[0] = consts.StatusDash
					}
				}
			}
		}

		// 设置不可点击状态
		if homeworkArray[0] == consts.StatusDash {
			homeworkArray[2] = 0
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取作业数据】", "DAS: homeworkStatus + LU: homeworkLevel + 作业绑定数据 + 开启状态 + ILab")
	return
}

// GetHomeworkLikeData 获取相似题数据
// 对应PHP中的similarHomework字段，格式化为["状态", "颜色", "是否可点击"]
func (s *Format) GetHomeworkLikeData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	// 获取LU数据 - 相似题字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取作业绑定数据
	hwBindExams, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkBindExams", []interface{}{
		s.param.LessonIDs,
		consts.BindTypeHomework, // 作业绑定类型
	})
	if err != nil {
		return err
	}

	// 获取作业开启信息
	// 需要先获取课程章节信息来构建lessonList
	lessonList, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonList", []interface{}{
		s.param.CourseID,
	})
	if err != nil {
		lessonList = make(map[int64]interface{})
	}

	homeworkOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkOpenInfo", []interface{}{
		s.param.CourseID,
		lessonList,
	})
	if err != nil {
		homeworkOpenInfo = make(map[int64]jxexamui.HomeworkOpenInfo)
	}

	// 类型转换
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)
	bindExams := hwBindExams.(map[int64]bool)
	openInfo := homeworkOpenInfo.(map[int64]jxexamui.HomeworkOpenInfo)

	// 处理每个章节的相似题数据
	for _, lessonID := range s.param.LessonIDs {
		similarHomeworkArray := []interface{}{consts.StatusDash, consts.ColorGray, 1}

		// 获取当前章节的LU数据
		lessonLuData, hasLuData := luData[lessonID]
		isBindHw := bindExams[lessonID]

		// 检查作业是否开启
		isHomeworkOpen := false
		if openInfoData, exists := openInfo[lessonID]; exists {
			isHomeworkOpen = openInfoData.IsOpen == consts.HomeworkOpen
		}

		if isHomeworkOpen && isBindHw && hasLuData {
			// 获取相似题数据 - 使用点分隔的字段名直接访问
			var correctLevel, correctStatus int

			// 从原始map中获取exam_answer.exam33.correct_level字段
			if correctLevelInterface, exists := lessonLuData.ExamAnswer["exam33"]["correct_level"]; exists {
				if correctLevelFloat, ok := correctLevelInterface.(float64); ok {
					correctLevel = int(correctLevelFloat)
				} else if correctLevelInt, ok := correctLevelInterface.(int64); ok {
					correctLevel = int(correctLevelInt)
				}
			}

			// 从原始map中获取exam_answer.exam33.last_correct_status字段
			if correctStatusInterface, exists := lessonLuData.ExamAnswer["exam33"]["last_correct_status"]; exists {
				if correctStatusFloat, ok := correctStatusInterface.(float64); ok {
					correctStatus = int(correctStatusFloat)
				} else if correctStatusInt, ok := correctStatusInterface.(int64); ok {
					correctStatus = int(correctStatusInt)
				}
			}

			// 检查是否在等待显示状态中
			if consts.ExamCorrectStatusWaitShowMap[correctStatus] {
				// 显示订正状态文本
				if statusText, exists := consts.ExamCorrectStatusMap[correctStatus]; exists {
					similarHomeworkArray[0] = statusText
				} else {
					similarHomeworkArray[0] = consts.StatusDash
				}
			} else {
				// 显示等级
				if levelText, exists := consts.HomeworkLevelMap[int64(correctLevel)]; exists && correctLevel > 0 {
					similarHomeworkArray[0] = levelText
					similarHomeworkArray[1] = consts.ColorOrange // 不满分
				} else {
					similarHomeworkArray[0] = consts.StatusNoLevel
				}
			}

			// 检查是否为满分（在整个if条件内，与PHP逻辑一致）
			if similarHomeworkArray[0] == consts.HomeworkFullMarksCode {
				similarHomeworkArray[1] = consts.ColorGreen // 满分
			}
		} else {
			// 作业未开启或未绑定，设置不可点击
			similarHomeworkArray[2] = 0
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, similarHomeworkArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取相似题数据】", "LU: exam_answer.exam33.* + 作业绑定数据 + 作业开启状态")
	return
}

// GetExerciseColumn 获取互动题数据
// 对应PHP中的exercise字段，格式化为"正确数|参与数|总数"
func (s *Format) GetExerciseColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_right_cnt", "inclass_participate_cnt", "inclass_question_cnt"}) {
		return
	}

	// 获取DAS学生章节数据
	dasLessonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonsData", []interface{}{
		[]int64{s.param.StudentUid},
		s.param.LessonIDs,
	})
	if err != nil {
		return err
	}

	// 获取LU通用数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	dasData := dasLessonData.(map[int64]map[int64]*das.StudentLessonInfo)
	luData := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 处理每个章节的互动题数据
	for _, lessonID := range s.param.LessonIDs {
		exerciseArray := []interface{}{consts.StatusDash, consts.ColorGray, 1}

		// 获取当前章节的数据
		var isAttended bool
		if studentData, exists := dasData[s.param.StudentUid]; exists {
			if lessonData, exists := studentData[lessonID]; exists {
				isAttended = lessonData.IsAttended == 1
			}
		}

		lessonLuData, hasLuData := luData[lessonID]
		if !hasLuData {
			// 没有LU数据时设置默认值
			exerciseArray[0] = consts.StatusDash
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseArray)
			continue
		}

		// 计算互动题总数
		intInteractTotalNum := lessonLuData.InclassQuestionCnt

		// 格式化互动题数据
		exerciseArray[0] = fmt.Sprintf(consts.LessonExerciseDetail,
			lessonLuData.InclassRightCnt,
			lessonLuData.InclassParticipateCnt,
			intInteractTotalNum)

		// 根据到课状态设置颜色（这里简化处理，实际可能需要更复杂的逻辑）
		if !isAttended {
			exerciseArray[1] = consts.ColorGray // 未参与时设置为灰色
		} else if intInteractTotalNum > 0 && lessonLuData.InclassRightCnt == intInteractTotalNum {
			exerciseArray[1] = consts.ColorGreen // 全答全对时设置为绿色
		} else {
			exerciseArray[1] = consts.ColorOrange // 其他情况设置为橙色
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取互动题数据】", "DAS: isAttended + LU: inclass_* + 考试绑定数据 + 灰度测试")
	return
}

// GetExerciseAllColumn 获取观看互动题数据（课中+回放）
// 对应PHP中的exerciseAll字段，格式化为"(课中正确数+回放正确数)|(课中参与数+回放参与数)|总数"
func (s *Format) GetExerciseAllColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_right_cnt", "inclass_participate_cnt", "inclass_question_cnt", "playback_right_cnt", "playback_participate_cnt"}) {
		return
	}

	// 获取LU通用数据（包含课中和回放数据）
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	luData := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 处理每个章节的观看互动题数据
	for _, lessonID := range s.param.LessonIDs {
		exerciseAllArray := []interface{}{consts.StatusDash, consts.ColorGray, 1}

		lessonLuData, hasLuData := luData[lessonID]

		if !hasLuData {
			// 没有LU数据时设置默认值
			exerciseAllArray[0] = consts.StatusDash
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseAllArray)
			continue
		}

		// 计算互动题总数
		intInteractTotalNum := lessonLuData.InclassQuestionCnt

		// 如果总数为0，显示"-"
		if intInteractTotalNum == 0 {
			exerciseAllArray[0] = consts.StatusDash
		} else {
			// 格式化观看互动题数据（课中+回放）
			exerciseAllArray[0] = fmt.Sprintf(consts.LessonExerciseDetail,
				lessonLuData.InclassRightCnt+lessonLuData.PlaybackRightCnt,
				lessonLuData.InclassParticipateCnt+lessonLuData.PlaybackParticipateCnt,
				intInteractTotalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseAllArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取观看互动题数据】", "LU: inclass_* + playback_* + 考试绑定数据 + 灰度测试")
	return
}

// GetLbpInteractExamColumn 获取LBP互动题数据
// 对应PHP中的lbpInteractExam字段，格式化为"正确数|提交数|总数"
func (s *Format) GetLbpInteractExamColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"mix_live_interaction_right_num", "mix_live_interaction_submit_num"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_question_cnt"}) {
		return
	}

	// 获取LU数据（包含LBP互动题数据）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取LU通用数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	luStudentData := luData.(map[int64]*dataproxy.GetLuDataResp)
	luCommonDataMap := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 处理每个章节的LBP互动题数据
	for _, lessonID := range s.param.LessonIDs {
		lbpInteractArray := []interface{}{consts.StatusDash, consts.ColorGray, 1}

		lessonLuData, hasLuData := luStudentData[lessonID]
		if !hasLuData {
			// 没有LU数据时设置默认值
			lbpInteractArray[0] = consts.StatusDash
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpInteractArray)
			continue
		}

		// 计算互动题总数
		intInteractTotalNum := int64(0)
		if commonData, exists := luCommonDataMap[lessonID]; exists {
			intInteractTotalNum = commonData.InclassQuestionCnt
		}

		// 如果总数为0，显示"-"
		if intInteractTotalNum == 0 {
			lbpInteractArray[0] = consts.StatusDash
		} else {
			// 格式化LBP互动题数据
			lbpInteractArray[0] = fmt.Sprintf(consts.LessonExerciseDetail,
				lessonLuData.MixLiveInteractionRightNum,
				lessonLuData.MixLiveInteractionSubmitNum,
				intInteractTotalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpInteractArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP互动题数据】", "LU: mix_live_interaction_* + 章节数据: mix_interaction_total_num + 灰度测试")
	return
}

// GetMixPlaybackInteract 获取融合回放互动题数据
// 对应PHP中的mixPlaybackInteract字段，格式化为"正确数|提交数|总数"
func (s *Format) GetMixPlaybackInteract(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"mix_playback_interaction_right_num", "mix_playback_interaction_submit_num"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLesson, []string{"lessonId", "mix_interaction_total_num"}) {
		return
	}

	// 获取LU数据（包含融合回放互动题数据）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取章节数据（获取总数）
	lessonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonDataByLessonIds", []interface{}{s.param.LessonIDs})
	if err != nil {
		return err
	}

	// 类型转换
	luStudentData := luData.(map[int64]*dataproxy.GetLuDataResp)
	lessonDataMap := lessonData.(map[int64]*dataproxy.GetLessonDataByLessonIdsResp)

	// 处理每个章节的融合回放互动题数据
	for _, lessonID := range s.param.LessonIDs {
		mixPlaybackArray := []interface{}{consts.StatusDash, consts.ColorGray, 1}

		lessonLuData, hasLuData := luStudentData[lessonID]
		if !hasLuData {
			// 没有LU数据时设置默认值
			mixPlaybackArray[0] = consts.StatusDash
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, mixPlaybackArray)
			continue
		}

		// 获取总数
		var totalNum int64
		if lessonInfo, exists := lessonDataMap[lessonID]; exists {
			totalNum = lessonInfo.MixInteractionTotalNum
		}

		// 如果总数为0，显示"-"
		if totalNum == 0 {
			mixPlaybackArray[0] = consts.StatusDash
		} else {
			// 格式化融合回放互动题数据
			mixPlaybackArray[0] = fmt.Sprintf(consts.LessonExerciseDetail,
				lessonLuData.MixPlaybackInteractionRightNum,
				lessonLuData.MixPlaybackInteractionSubmitNum,
				totalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, mixPlaybackArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取融合回放互动题数据】", "LU: mix_playback_interaction_* + 章节数据: mix_interaction_total_num")
	return
}
