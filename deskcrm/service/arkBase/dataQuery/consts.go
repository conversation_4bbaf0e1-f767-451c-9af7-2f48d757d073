package dataQuery

const (
	PreviewDepartment5  = 5  // 小学预习
	PreviewDepartment13 = 13 // 初高中预习
)

// 预习部门映射关系 - 对应PHP版本的$previewDepartmentMap
var PreviewDepartmentMap = map[int64]int64{
	GradeStagePreschool: PreviewDepartment5,  // 学前 -> 小学预习
	GradeStagePrimary:   PreviewDepartment5,  // 小学 -> 小学预习
	GradeStageJunior:    PreviewDepartment13, // 初中 -> 初高中预习
	GradeStageSenior:    PreviewDepartment13, // 高中 -> 初高中预习
}

const (
	GradeStagePrimary   = 1   // 小学
	GradeStageJunior    = 20  // 初中
	GradeStageSenior    = 30  // 高中
	GradeStagePreschool = 60  // 学前
	GradeStageAdult     = 70  // 成人
	GradeStageDaxue     = 80  // 大学
	GradeStageDiyou     = 90  // 低幼
	GradeStageOther     = 255 // 其他
)

// 定义试卷模块相关常量
const (
	BindTypePracticeInClass    = 1  // 课中练习 别名：互动题
	BindTypeFinal              = 2  // 期末考试（废弃）
	BindTypePreTest            = 3  // 报前测试
	BindTypePostTest           = 4  // 报后测试
	BindTypePreview            = 5  // 课前预习 小学预习 小学课前预习
	BindTypeReview             = 6  // 课后复习（废弃）
	BindTypeHomework           = 7  // 课后作业
	BindTypeMission            = 8  // 任务（废弃）
	BindTypeStage              = 9  // 阶段性测试
	BindTypeTestInClass        = 10 // 堂堂测
	BindTypePrimaryMathPreview = 11 // 小学-数学-同步练习 as 同步练习
	BindTypeMockExam           = 12 // 模考
	BindTypePosttestMore       = 13 // 初高中预习测试 as 预习
	BindTypeWordPractice       = 14 // 单词练习
	BindTypeWordLearn          = 15 // 单词学习
	BindTypePractises          = 17 // 练一练
	BindTypeOutTest            = 18 // 出门测试
	BindTypeImproveTest        = 19 // 提升训练
	BindTypeSyntasTest         = 21 // 语法练习
	BindTypeInTest             = 23 // 入门测
	BindTypePDF                = 26 // 初高预习的PDF
	BindTypeHomeworkIlab       = 31 // ilab巩固练习升级绑
	BindTypeOralQuestion       = 32 // 口述题
	BindTypeHomeworkSimilars   = 33 // 相似题
	BindTypeInnerTrainStage    = 34 // 内训阶段测
	BindTypeExamType36         = 36 // 日积月累 采薇-赏析
	BindTypeExamType37         = 37 // 日积月累 采薇-快速阅读
	BindTypeExamType38         = 38 // 日积月累 充电小讲堂
	BindTypeExamType27         = 27 // 每日一练

	RelationTypeLesson = 0 // 绑定类型为章节
	RelationTypeCourse = 1 // 绑定类型为课程
	RelationTypeCPU    = 2 // 绑定类型为CPU
	RelationTypeStage  = 3 // 绑定类型为阶段
)

var getTotalNumExamTypes []int64 = []int64{
	BindTypeTestInClass,
	BindTypePracticeInClass,
	BindTypePrimaryMathPreview,
	BindTypeOralQuestion,
	BindTypeHomework,
	BindTypePreview,
	BindTypePosttestMore,
}
